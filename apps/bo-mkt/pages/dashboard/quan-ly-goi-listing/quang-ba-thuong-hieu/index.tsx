import {
  MPBrandPromotion,
  openNotification,
  showError
} from '@medpro-libs/medpro-component-libs'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useClientSDK } from '../../../../hooks/useClientSDK'
import { useAppSelector } from '../../../../store/hooks'
import { hospitalActions } from '../../../../store/hospital/slice'

export default function BrandPromotionPage(props: any) {
  const { session, heightContent, environment } = props
  const { client } = useClientSDK(session)
  const dispatch = useDispatch()
  const loading = useAppSelector((state) => state?.total?.loading)
  const hospitalLisTab = useAppSelector((state) => state?.hospital?.list)
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const [filter, setFilter] = useState<any>({
    pageIndex: 0,
    pageSize: 10
  })
  const [packageList, setPackageList] = useState<any>([])
  const [dataPartner, setDataPartner] = useState<any>([])
  const [dataCSYTFavourite, setDataCSYTFavourite] = useState<any>([])
  const [selectedFilterTab, setSelectedFilterTab] = useState<any>('ALL')
  const [tabActiveKey, setTabActiveKey] = useState('partner')
  const [loadingDoctorTelemed, setLoadingDoctorTelemed] =
    useState<boolean>(false)
  const [serviceInMonthList, setServiceInMonthList] = useState<any>([])
  const [loadingServiceInMonth, setLoadingServiceInMonth] =
    useState<boolean>(false)
  const [allServiceInMonthList, setAllServiceInMonthList] = useState<any>([])
  const [doctorTelemedList, setDoctorTelemedList] = useState<any>([])
  const [allDoctorTelemedList, setAllDoctorTelemedList] = useState<any>([])
  const [category, setCategory] = useState<string>('suc-khoe')

  // Sắp xếp table
  const onDragSortTable = async (data: any) => {
    if (data.length <= 1) {
      return false
    }
    setFetchLoading(false)
    try {
      const ids = data && data.length ? data.map((item: any) => item._id) : []
      await client?.homepageModule.sortOrderHomePgaeModule(
        {
          ids,
          repo: environment.code,
          type: tabActiveKey,
          category: category
        },
        { appid: 'medpro' }
      )
      openNotification('success', { message: 'Thao tác thành công' })
      // await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const fetchDataPartner = useCallback(async () => {
    try {
      const response = await client?.homepageModule.getPackageList(
        {
          repo: environment.code,
          tab: selectedFilterTab
        },
        { appid: 'medpro' }
      )
      setDataPartner(response?.data)
    } catch (error) {
      console.error('Lỗi khi lấy danh sách gói:', error)
    }
  }, [client, selectedFilterTab, environment])

  const fetchDoctorTelemed = useCallback(async () => {
    try {
      await methodsDoctorTelemed.getListDoctorTelemed({ tab: 'ALL' })
    } catch (error) {
      console.error('Error fetching popup list:', error)
    }
  }, [client?.homepageModule])

  const fetchServiceInMonth = useCallback(async () => {
    try {
      await methodsServiceInMonth.getListServiceInMonth({
        tabs: 'ALL',
        category: 'suc-khoe'
      })
    } catch (error) {
      console.error('Error fetching popup list:', error)
    }
  }, [client?.homepageModule])

  const fetchCSYTFavourite = useCallback(async () => {
    try {
      const response = await client?.homepageModule.getHospitalList(
        {
          repo: environment.code,
          tab: selectedFilterTab
        },
        { appid: 'medpro' }
      )
      setDataCSYTFavourite(response?.data)
    } catch (error) {
      console.error('Lỗi khi lấy danh sách cơ sở tế yêu thích:', error)
    }
  }, [client, selectedFilterTab, environment])

  useEffect(() => {
    fetchDataPartner()
  }, [fetchDataPartner])

  useEffect(() => {
    fetchDoctorTelemed()
  }, [fetchDoctorTelemed])

  useEffect(() => {
    fetchServiceInMonth()
  }, [fetchServiceInMonth])

  useEffect(() => {
    dispatch(hospitalActions.getHospitalList({ ...session }))
    setFetchLoading(false)
  }, [session, dispatch])

  const handleFilter = (newFilter: any) => {
    setFilter((prevFilter: any) => {
      return {
        ...prevFilter,
        ...newFilter
      }
    })
    // setFetchLoading(true)
  }

  const methodsDoctorTelemed = {
    getListDoctorTelemed: async ({ tab }: { tab: string }) => {
      setLoadingDoctorTelemed(true)
      try {
        const response = await client?.homepageModule.getListDoctorTelemed(
          {
            repo: environment.code,
            tab
          },
          { appid: 'medpro' }
        )
        setDoctorTelemedList(response?.data)
      } catch (error) {
        showError(error)
      } finally {
        setLoadingDoctorTelemed(false)
      }
    },

    getAllDoctorTelemed: async () => {
      try {
        const response = await client?.homepageModule.getAllDoctorTelemed({
          treeId: 'TELEMED'
        })
        setAllDoctorTelemedList(response?.data?.results)
      } catch (error) {
        showError(error)
      }
    },
    onAdd: async (body: any) => {
      try {
        await client?.homepageModule.addDoctorTelemed({
          ...body,
          repo: environment.code
        })
        openNotification('success', { message: 'Thêm mới bác sĩ thành công' })

        methodsDoctorTelemed.getListDoctorTelemed({ tab: 'ALL' })
      } catch (error) {
        showError(error)
      }
    },
    onUpdate: async (body: any) => {
      try {
        await client?.homepageModule.updateDoctorTelemed({
          ...body,
          repo: environment.code
        })
        openNotification('success', { message: 'Cập nhật bác sĩ thành công' })

        methodsDoctorTelemed.getListDoctorTelemed({ tab: 'ALL' })
      } catch (error) {
        showError(error)
      }
    },

    onDelete: async (id: string) => {
      try {
        await client?.homepageModule.deleteDoctorTelemed(id)
        openNotification('success', { message: 'Xóa bác sĩ thành công' })

        methodsDoctorTelemed.getListDoctorTelemed({ tab: 'ALL' })
      } catch (error) {
        showError(error)
      }
    }
  }

  const actionCompanionship = {
    create: async (values: any, cancelModal: any) => {
      setFetchLoading(true)
      try {
        await client?.homepageModule.createPackage(
          {
            ...values,
            repo: 'testing'
          },
          {
            appid: 'medpro'
          }
        )
        openNotification('success', { message: 'Tạo mới thành công' })
        onRefresh()
        cancelModal()
        fetchDataPartner()
        setFetchLoading(false)
      } catch (error) {
        showError(error)
        setFetchLoading(false)
      }
    },
    update: async (values: any, cancelModal: any) => {
      setFetchLoading(true)
      try {
        await client?.homepageModule.updatePackage(values._id, {
          ...values
        })
        openNotification('success', { message: 'Cập nhật thành công' })
        onRefresh()
        cancelModal()
        fetchDataPartner()
        setFetchLoading(false)
      } catch (error) {
        showError(error)
        setFetchLoading(false)
      }
    },
    delete: async (id: string) => {
      setFetchLoading(true)
      try {
        await client?.homepageModule.deletePackage(id)
        openNotification('success', { message: 'Xóa thành công' })
        onRefresh()
        fetchDataPartner()
        setFetchLoading(false)
      } catch (error) {
        showError(error)
        setFetchLoading(false)
      }
    }
  }

  const methodsServiceInMonth = {
    getListServiceInMonth: async ({ tab, category }: any) => {
      setLoadingServiceInMonth(true)
      try {
        const response = await client?.homepageModule.getListServiceInMonth({
          repo: environment.code,
          tab: tab,
          category: category
        })
        setServiceInMonthList(response?.data)
      } catch (error) {
        showError(error)
      } finally {
        setLoadingServiceInMonth(false)
      }
    },
    getAllServiceInMonth: async ({ partnerId }: any) => {
      try {
        const response = await client?.service.getPackageList({
          partnerId
        })
        setAllServiceInMonthList(response?.data)
      } catch (error) {
        showError(error)
      }
    },
    onAdd: async (values: any) => {
      try {
        await client?.homepageModule.addServiceInMonth({
          ...values,
          repo: environment.code
        })
        openNotification('success', { message: 'Thêm mới thành công' })
        methodsServiceInMonth.getListServiceInMonth({
          tab: 'ALL',
          category: values.category
        })
      } catch (error) {
        showError(error)
      }
    },
    onUpdate: async (values: any) => {
      try {
        await client?.homepageModule.updateServiceInMonth({
          ...values,
          repo: environment.code
        })
        openNotification('success', { message: 'Cập nhật thành công' })
        methodsServiceInMonth.getListServiceInMonth({
          tab: values.tab,
          category: values.category
        })
      } catch (error) {
        showError(error)
      }
    },
    onDelete: async ({ id, tab, category }: any) => {
      try {
        await client?.homepageModule.deleteServiceInMonth(id)
        openNotification('success', { message: 'Xóa thành công' })

        methodsServiceInMonth.getListServiceInMonth({
          tab,
          category: category
        })
      } catch (error) {
        showError(error)
      }
    }
  }
  // Handle Cơ sở y tế yêu thích
  const actionCSYTFavourite = {
    getList: async () => {
      try {
        const response = await client?.homepageModule.getHospitalList(
          {
            repo: environment.code
          },
          { appid: 'medpro' }
        )
        setDataCSYTFavourite(response?.data)
      } catch (error) {
        console.error('Lỗi khi lấy danh sách cơ sở tế yêu thích:', error)
      }
    },
    create: async (values: any, cancelModal: any) => {
      setFetchLoading(true)
      try {
        await client?.homepageModule.createHospital(
          {
            ...values,
            repo: 'testing'
          },
          {
            appid: 'medpro'
          }
        )
        openNotification('success', { message: 'Tạo mới thành công' })
        onRefresh()
        cancelModal()
        fetchCSYTFavourite()
        setFetchLoading(false)
      } catch (error) {
        showError(error)
        setFetchLoading(false)
      }
    },
    update: async (values: any, cancelModal: any) => {
      setFetchLoading(true)
      try {
        await client?.homepageModule.updateHospital(values._id, {
          ...values
        })
        openNotification('success', { message: 'Cập nhật thành công' })
        onRefresh()
        cancelModal()
        fetchCSYTFavourite()
        setFetchLoading(false)
      } catch (error) {
        showError(error)
      }
    },
    delete: async (id: string) => {
      setFetchLoading(true)
      try {
        await client?.homepageModule.deleteHospital(id)
        openNotification('success', { message: 'Xóa thành công' })
        onRefresh()
        fetchCSYTFavourite()
        setFetchLoading(false)
      } catch (error) {
        showError(error)
        setFetchLoading(false)
      }
    }
  }

  const onRefresh = async () => {
    console.log(11111, 'hello-12')
  }

  const onPressViewDetail = async (id: string) => {
    console.log(11111, 'hello-1')
  }

  const onChangePageEvent = (value: any) => {
    handleFilter({
      pageIndex: value - 1
    })
  }

  const onChangeSizeEvent = (value: any) => {
    handleFilter({
      pageSize: value,
      pageIndex: 0
    })
  }

  const onChangeTabEvent = async (value: any, level?: any) => {
    if (level === 'generalTab') {
      setTabActiveKey(value)
      switch (value) {
        case 'partner':
          await fetchDataPartner()
          break
        case 'hospitals':
          await fetchCSYTFavourite()
          break
        default:
          break
      }
    }
    if (level === 'filterTab') {
      setSelectedFilterTab(value)
    }
  }

  const baseValueDoctorTelemed = useMemo(() => {
    return {
      doctorTelemedList: doctorTelemedList,
      allDoctorTelemedList: allDoctorTelemedList
    }
  }, [doctorTelemedList, allDoctorTelemedList])

  const baseValueServiceInMonth = useMemo(() => {
    return {
      serviceInMonthList: serviceInMonthList,
      allServiceInMonthList: allServiceInMonthList
    }
  }, [serviceInMonthList, allServiceInMonthList])
  return (
    <MPBrandPromotion
      onDragSortTable={onDragSortTable}
      heightContent={heightContent}
      hospitalListTab={hospitalLisTab}
      packageList={packageList}
      loading={fetchLoading || loading?.status}
      onPressViewDetail={onPressViewDetail}
      // TTHT và đồng hành
      dataPartner={dataPartner}
      actionCompanionship={actionCompanionship}
      // Cơ sở y tế yêu thích
      dataCSYTFavourite={dataCSYTFavourite}
      actionCSYTFavourite={actionCSYTFavourite}
      // package-in-month
      baseValueServiceInMonth={baseValueServiceInMonth}
      loadingServiceInMonth={loadingServiceInMonth}
      methodsServiceInMonth={methodsServiceInMonth}
      // doctor-in-month
      loadingDoctorTelemed={loadingDoctorTelemed}
      baseValueDoctorTelemed={baseValueDoctorTelemed}
      methodsDoctorTelemed={methodsDoctorTelemed}
      category={category}
      setCategory={setCategory}
      // ------------------------------------------
      pagination={{
        current: filter.pageIndex > 0 ? filter.pageIndex + 1 : 1,
        pageSize: filter.pageSize,
        total:
          hospitalLisTab && hospitalLisTab?.length ? hospitalLisTab?.length : 0
      }}
      onChangePageEvent={onChangePageEvent}
      onChangeSizeEvent={onChangeSizeEvent}
      onChangeTabEvent={onChangeTabEvent}
      setTabActiveKey={setTabActiveKey}
      tabActiveKey={tabActiveKey}
    />
  )
}
