import { TablePaginationConfig } from 'antd'
import MPBrandPromotionComponent from '../../components/MPBrandPromotionComponent'
import MPBrandPromotionCard from '../../ui/MPBrandPromotionCard'

export interface BrandPromotionProps {
  onDragSortTable?: any
  heightContent: number
  hospitalListTab: any
  packageList: any
  pagination?: TablePaginationConfig
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  // TTHT và đồng hành
  dataPartner: any
  actionCompanionship: any
  // Cơ sở y tế yêu thích
  dataCSYTFavourite: any
  actionCSYTFavourite: any
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  onChangeTabEvent: (key: any, level: any) => void
  baseValueDoctorTelemed: any
  loadingDoctorTelemed: boolean
  methodsDoctorTelemed: any
  category: any
  setCategory: any
  setTabActiveKey: any
  tabActiveKey: any
  baseValueServiceInMonth: any
  loadingServiceInMonth: boolean
  methodsServiceInMonth: any
}

export const MPBrandPromotion = ({
  onDragSortTable,
  heightContent,
  hospitalListTab,
  packageList,
  loading,
  onPressViewDetail,
  dataPartner,
  actionCompanionship,
  dataCSYTFavourite,
  actionCSYTFavourite,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent,
  onChangeTabEvent,
  baseValueDoctorTelemed,
  loadingDoctorTelemed,
  methodsDoctorTelemed,
  category,
  setCategory,
  setTabActiveKey,
  tabActiveKey,
  baseValueServiceInMonth,
  loadingServiceInMonth,
  methodsServiceInMonth
}: BrandPromotionProps) => {
  return (
    <MPBrandPromotionComponent
      renderItem={() => {
        return (
          <MPBrandPromotionCard
            onDragSortTable={onDragSortTable}
            heightContent={heightContent}
            hospitalListTab={hospitalListTab}
            packageList={packageList}
            loading={loading}
            onPressViewDetail={onPressViewDetail}
            dataPartner={dataPartner}
            actionCompanionship={actionCompanionship}
            dataCSYTFavourite={dataCSYTFavourite}
            actionCSYTFavourite={actionCSYTFavourite}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
            onChangeTabEvent={onChangeTabEvent}
            baseValueDoctorTelemed={baseValueDoctorTelemed}
            loadingDoctorTelemed={loadingDoctorTelemed}
            methodsDoctorTelemed={methodsDoctorTelemed}
            category={category}
            setCategory={setCategory}
            setTabActiveKey={setTabActiveKey}
            tabActiveKey={tabActiveKey}
            baseValueServiceInMonth={baseValueServiceInMonth}
            loadingServiceInMonth={loadingServiceInMonth}
            methodsServiceInMonth={methodsServiceInMonth}
          />
        )
      }}
    />
  )
}
