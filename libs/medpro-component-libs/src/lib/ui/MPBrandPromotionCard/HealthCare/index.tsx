import React, { useEffect, useMemo, useRef, useState } from 'react'
import {
  Form,
  Modal,
  Radio,
  Space,
  Switch,
  type TableColumnsType,
  TablePaginationConfig,
  Tabs
} from 'antd'
import cx from 'classnames'
import { FiPlus } from 'react-icons/fi'
import dayjs from 'dayjs'
import {
  MPButton,
  normalizeTable,
  useDynamicContentHeight
} from '@medpro-libs/medpro-component-libs'
import { Image } from 'antd'
import MPTable, { DragHandle } from '../../../atoms/MPTable'
import styles from '../styles.module.less'
import { FaPen } from 'react-icons/fa'
import { handleBase, handleBaseEdit } from './handleBase'

export interface Props {
  onDragSortTable?: any
  headerRef: any
  heightContent: number
  hospitalList: any
  packageList: any
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  category: any
  setCategory: any
  baseValueServiceInMonth: any
  loadingServiceInMonth: boolean
  methodsServiceInMonth: any
}

const HealthCare = ({
  onDragSortTable,
  headerRef,
  heightContent,
  hospitalList,
  packageList,
  loading,
  onPressViewDetail,
  pagination,
  category,
  setCategory,
  onChangePageEvent,
  onChangeSizeEvent,
  baseValueServiceInMonth,
  loadingServiceInMonth,
  methodsServiceInMonth
}: Props) => {
  const filterRef = useRef<HTMLDivElement>(null)
  const { heightContent: dynHeightContent, heightTbBody } =
    useDynamicContentHeight([headerRef.current, filterRef.current], {
      key: 'table',
      heightContent: heightContent - 70
    })
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false)
  const [action, setAction] = useState('')
  const [tabActiveKey, setTabActiveKey] = useState('ALL')
  const [selectedPackage, setSelectedPackage] = useState<any>(null)

  const onPressDelete = (row: any) => {
    Modal.confirm({
      title: 'Xác nhận xóa!',
      content: `Bạn có chắc chắn muốn xóa ${row.name}?`,
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      centered: true,
      onOk() {
        return methodsServiceInMonth.onDelete({
          id: row._id,
          tab: tabActiveKey,
          category: category
        })
      }
    })
  }

  const onPressEdit = (row: any) => {
    setSelectedPackage(row)
    toggleModal('edit')
  }

  const onPressNew = () => {
    toggleModal('create')
    // methodsServiceInMonth.getAllServiceInMonth({ partnerId: 'drcheck' })
  }

  const dataSource = useMemo(() => {
    return baseValueServiceInMonth.serviceInMonthList.map(
      (item: any, index: number) => {
        return {
          ...item,
          key: item.packageId,
          // Add display information for the table
          displayPeriod: {
            fromDate: item.fromDate,
            toDate: item.toDate
          },
          status: item.status
        }
      }
    )
  }, [baseValueServiceInMonth])

  const columns: TableColumnsType<any> = normalizeTable(
    [
      {
        title: 'STT',
        width: 70,
        key: 'position',
        align: 'center',
        fixed: 'left',
        onCell: () => ({ style: { padding: '12px 10px' } }),
        render: (text: any, record: any, index: number) => {
          return <div className={styles['groupPosition']}>{index + 1}</div>
        }
      },
      {
        title: 'Thông tin gói khám',
        width: 350,
        key: 'name',
        fixed: 'left',
        render: (row: any) => {
          return (
            <div className={styles['itemInfo']}>
              {tabActiveKey === 'ALL' && <DragHandle />}
              <div className={styles['itemContent']}>
                <div className={styles['itemImage']}>
                  <Image
                    src={row.imageUrl}
                    alt={row.title}
                    width={50}
                    height={50}
                  />
                </div>
                <div className={styles['itemText']}>{row.title}</div>
              </div>
            </div>
          )
        }
      },
      {
        title: 'CSYT',
        key: 'hospital',
        width: 80,
        render: (row: any) => (
          <div className={styles['groupHospital']}>
            <Image
              src={row.circleLogo}
              alt={row.desc2}
              width={50}
              height={50}
            />
          </div>
        )
      },
      {
        title: 'Thời gian hiển thị',
        key: 'displayPeriod',
        width: 200,
        render: (row: any) => (
          <Space direction='vertical' size={8}>
            <div className={styles['groupPeriod']}>
              {row?.fromDate && (
                <div className={styles['start']}>
                  <p>{dayjs(row?.fromDate).format('HH:mm, DD/MM/YYYY')}</p>
                </div>
              )}
              <div className={styles['line']} />
              {row?.toDate && (
                <div className={styles['end']}>
                  <p>{dayjs(row?.toDate).format('HH:mm, DD/MM/YYYY')}</p>
                </div>
              )}
            </div>
            <div
              className={cx(styles['groupStatus'], styles['status'])}
              style={{
                color: row?.tabStatus?.style?.color,
                backgroundColor: row?.tabStatus?.style?.backgroundColor,
                border: row?.tabStatus?.style?.border
              }}
            >
              {row?.tabStatus?.text}
            </div>
          </Space>
        )
      },
      {
        title: 'Bật / Tắt ghim',
        key: 'pin',
        align: 'center',
        width: 120,
        render: (row) => <Switch disabled checked={row.pin} />
      },
      {
        title: 'Thao tác',
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: 120,
        actionType: 'actionButton',
        actionDisplay: ['edit', 'delete']
      }
    ],
    onPressEdit,
    onPressDelete
  )

  const items = [
    {
      key: 'ALL',
      label: 'Tất cả',
      forceRender: true,
      children: null
    },
    {
      key: 'INPROGRESS',
      label: 'Đang hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: 'PENDING',
      label: 'Chờ hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: 'EXPIRED',
      label: 'Hết hạn',
      forceRender: true,
      children: null
    }
  ]

  const itemsCategory = [
    {
      value: 'suc-khoe',
      label: 'Sức Khỏe'
    },
    {
      value: 'tiem-chung',
      label: 'Tiêm Chủng'
    },
    {
      value: 'xet-nghiem',
      label: 'Xét Nghiệm'
    }
  ]

  const renderTabPane = () => {
    switch (tabActiveKey) {
      case 'ALL':
        return (
          <MPTable
            type={'dragTable'}
            onDragSortTable={onDragSortTable}
            columns={columns}
            dataSource={dataSource}
            dynHeightContent={dynHeightContent}
            heightTbBody={heightTbBody}
            loading={loadingServiceInMonth}
            pagination={false}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      case 'INPROGRESS':
      case 'PENDING':
      case 'EXPIRED':
        return (
          <MPTable
            columns={columns}
            dataSource={dataSource}
            dynHeightContent={dynHeightContent}
            heightTbBody={heightTbBody}
            loading={loadingServiceInMonth}
            pagination={false}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      default:
        return null
    }
  }

  const onChangeTabEvent = (key: string) => {
    setTabActiveKey(key)
    void methodsServiceInMonth.getListServiceInMonth({
      tab: key,
      category: 'suc-khoe'
    })
  }

  const toggleModal = (value: string) => {
    setIsOpenModal(!isOpenModal)
    setAction(value)
  }

  const renderModal = (type: string) => {
    switch (action) {
      case 'create':
        return (
          <ModalAddPackageInMonth
            isOpenModal={isOpenModal}
            contentModal={contentModal}
            baseValueServiceInMonth={baseValueServiceInMonth}
            onCancel={() => {
              setIsOpenModal(false)
              setAction('')
            }}
            onSubmit={(values: any) => {
              methodsServiceInMonth.onAdd({ ...values, category })
              toggleModal('create')
            }}
            loading={loadingServiceInMonth}
            hospitalList={hospitalList}
            methodsServiceInMonth={methodsServiceInMonth}
          />
        )
      case 'edit':
        return (
          <ModalEditPackageInMonth
            isOpenModal={isOpenModal}
            contentModal={contentModal}
            baseValueServiceInMonth={baseValueServiceInMonth}
            onCancel={() => {
              setIsOpenModal(false)
              setAction('')
            }}
            onSubmit={(values: any) => {
              methodsServiceInMonth.onUpdate({
                ...values,
                category,
                tab: tabActiveKey
              })
              toggleModal('edit')
            }}
            loading={loadingServiceInMonth}
            initialValues={selectedPackage}
          />
        )
      default:
        return null
    }
  }

  const contentModal = {
    create: {
      title: 'Thêm gói khám',
      centered: true,
      footer: false,
      className: styles['modalNewWrapper'],
      width: 800
    },
    edit: {
      title: 'Chỉnh sửa Thông tin gói khám',
      centered: true,
      footer: false,
      className: styles['modalEditWrapper'],
      width: 800
    }
  }

  const onChangeCategory = (e: any) => {
    setCategory(e.target.value)
    methodsServiceInMonth.getListServiceInMonth({
      tab: tabActiveKey,
      category: e.target.value
    })
  }

  return (
    <>
      <div className={styles['compTabWrapper']}>
        <Tabs
          defaultActiveKey={'ALL'}
          onChange={onChangeTabEvent}
          tabPosition={'top'}
          tabBarGutter={12}
          items={items}
        />
      </div>
      <Radio.Group
        defaultValue={'suc-khoe'}
        onChange={onChangeCategory}
        className={styles['category']}
        optionType='button'
        options={itemsCategory}
      />
      <div ref={filterRef} className={styles['filterWrapper']}>
        <div className={styles['left']}>
          <div className={styles['recordCount']}>
            {dataSource.length} Gói khám
          </div>
        </div>
        {tabActiveKey === 'ALL' && (
          <div className={styles['right']}>
            <div className={styles['actionGroup']}>
              <MPButton
                onClick={() => {
                  methodsServiceInMonth.getListServiceInMonth({
                    tab: tabActiveKey,
                    category: category
                  })
                }}
                typeCustom={'cancel'}
              >
                <span>Tải lại</span>
              </MPButton>
              <MPButton onClick={onPressNew} typeCustom={'primary'}>
                <FiPlus />
                <span>Chọn gói khám</span>
              </MPButton>
            </div>
          </div>
        )}
      </div>
      {renderTabPane()}
      {renderModal(action)}
    </>
  )
}

interface ModalAddDoctorTelemedProps {
  isOpenModal: boolean
  contentModal: any
  baseValueServiceInMonth: any
  onCancel: () => void
  onSubmit: (values: any) => void
  loading: boolean
  initialValues?: any
  hospitalList?: any
  methodsServiceInMonth?: any
}

const ModalAddPackageInMonth = ({
  isOpenModal,
  contentModal,
  baseValueServiceInMonth,
  onCancel,
  onSubmit,
  loading,
  hospitalList,
  methodsServiceInMonth
}: ModalAddDoctorTelemedProps) => {
  const [form] = Form.useForm()

  const [timeType, setTimeType] = useState(true)

  useEffect(() => {
    if (form.getFieldValue('display')) {
      toggleTimeType(true)
    }
  }, [form])

  const toggleTimeType = (value: boolean) => {
    setTimeType(value)
  }

  const onFinish = (values: any) => {
    const body = {
      ...values,
      fromDate: !values.display ? dayjs(values?.displayPeriod[0]) : '',
      toDate: !values.display ? dayjs(values?.displayPeriod[1]) : '',
      status: values?.status ?? false
    }
    delete body.partnerId
    delete body.displayPeriod
    onSubmit(body)
    form.resetFields()
  }

  const handleListService = ({ partnerId }: any) => {
    methodsServiceInMonth.getAllServiceInMonth({ partnerId })
  }

  return (
    <Modal {...contentModal.create} open={isOpenModal} onCancel={onCancel}>
      <Form
        form={form}
        onFinish={onFinish}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
      >
        {handleBase({
          form,
          allServiceInMonthList: baseValueServiceInMonth.allServiceInMonthList,
          timeType,
          toggleTimeType,
          hospitalList,
          handleListService
        })
          .reduce((rows: any, item: any, index: number, array: any) => {
            if (!item.group) {
              rows.push([item])
            } else if (index % 2 === 0) {
              rows.push(array.slice(index, index + 2))
            }
            return rows
          }, [])
          .map((row: any, rowIndex: number) => (
            <div
              key={rowIndex}
              className={cx(
                styles['inputRow'],
                row.every((item: any) => item.hidden) ? styles['hidden'] : null
              )}
            >
              {row.map((item: any, itemIndex: number) => (
                <div key={itemIndex} className={styles['inputItem']}>
                  {typeof item?.enter === 'function' ? item.enter(item) : null}
                </div>
              ))}
            </div>
          ))}
        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton onClick={onCancel} typeCustom={'cancel'}>
              Đóng
            </MPButton>
          </Form.Item>
          <Form.Item>
            <MPButton
              htmlType='submit'
              typeCustom={'approval'}
              loading={loading}
            >
              Lưu
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  )
}

const ModalEditPackageInMonth = ({
  isOpenModal,
  contentModal,
  baseValueServiceInMonth,
  onCancel,
  onSubmit,
  loading,
  initialValues
}: ModalAddDoctorTelemedProps) => {
  const [form] = Form.useForm()
  const [timeType, setTimeType] = useState(false)

  useEffect(() => {
    if (form.getFieldValue('display')) {
      toggleTimeType(true)
    }
  }, [form])

  const toggleTimeType = (value: boolean) => {
    setTimeType(value)
  }

  const onFinish = (values: any) => {
    const body = { ...values }
    if (body.display) {
      delete body.displayPeriod
    }
    delete body.avatar
    delete body.title
    onSubmit(body)
    form.resetFields()
  }

  const transformInitialValues = useMemo(() => {
    return {
      _id: initialValues?._id,
      packageId: initialValues?.packageId,
      display: Boolean(initialValues?.display),
      displayPeriod: [
        dayjs(initialValues?.fromDate),
        dayjs(initialValues?.toDate)
      ],
      status: initialValues.status,
      title: initialValues.title,
      avatar: initialValues.imageUrl
    }
  }, [initialValues])

  return (
    <Modal {...contentModal.edit} open={isOpenModal} onCancel={onCancel}>
      <Form
        form={form}
        onFinish={onFinish}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
        initialValues={transformInitialValues}
      >
        <Space direction='vertical' size={16}>
          {handleBaseEdit({
            form,
            allPackageInMonthList:
              baseValueServiceInMonth.allPackageInMonthList,
            timeType,
            toggleTimeType
          })
            .reduce((rows: any, item: any, index: number, array: any) => {
              if (!item.group) {
                rows.push([item])
              } else if (index % 2 === 0) {
                rows.push(array.slice(index, index + 2))
              }
              return rows
            }, [])
            .map((row: any, rowIndex: number) => (
              <div
                key={rowIndex}
                className={cx(
                  styles['inputRow'],
                  row.every((item: any) => item.hidden)
                    ? styles['hidden']
                    : null
                )}
              >
                {row.map((item: any, itemIndex: number) => (
                  <div key={itemIndex} className={styles['inputItem']}>
                    {typeof item?.enter === 'function'
                      ? item.enter(item)
                      : null}
                  </div>
                ))}
              </div>
            ))}
        </Space>
        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton onClick={onCancel} typeCustom={'cancel'}>
              Đóng
            </MPButton>
          </Form.Item>
          <Form.Item>
            <MPButton
              htmlType='submit'
              typeCustom={'approval'}
              loading={loading}
            >
              Lưu
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  )
}

export default HealthCare
