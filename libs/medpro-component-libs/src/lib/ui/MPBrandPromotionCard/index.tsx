import { getReplaceUTF8, MPButton } from '@medpro-libs/medpro-component-libs'
import { Select, TablePaginationConfig, Tabs } from 'antd'
import { useRef, useState } from 'react'
import Companionship from './Companionship'
import styles from './styles.module.less'
import CSYTFavourite from './CSYTFavourite'
import HealthCare from './HealthCare'
import DoctorConsultationVideo from './DoctorConsultationVideo'
import { log } from 'console'

export interface Props {
  onDragSortTable?: any
  heightContent: number
  hospitalListTab: any
  packageList: any
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  dataPartner: any
  actionCompanionship: any
  dataCSYTFavourite: any
  actionCSYTFavourite: any
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  onChangeTabEvent: (key: any, level: any) => void
  baseValueDoctorTelemed: any
  loadingDoctorTelemed: boolean
  methodsDoctorTelemed: any
  category: any
  setCategory: any
  setTabActiveKey: any
  tabActiveKey: any
  baseValueServiceInMonth: any
  loadingServiceInMonth: boolean
  methodsServiceInMonth: any
}

const MPBrandPromotionCard = ({
  onDragSortTable,
  heightContent,
  hospitalListTab,
  packageList,
  loading,
  onPressViewDetail,
  dataPartner,
  actionCompanionship,
  dataCSYTFavourite,
  actionCSYTFavourite,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent,
  onChangeTabEvent,
  baseValueDoctorTelemed,
  loadingDoctorTelemed,
  methodsDoctorTelemed,
  category,
  setCategory,
  setTabActiveKey,
  tabActiveKey,
  baseValueServiceInMonth,
  loadingServiceInMonth,
  methodsServiceInMonth
}: Props) => {
  const headerRef = useRef<HTMLDivElement>(null)
  const { Option } = Select

  const items = [
    {
      key: 'partner',
      label: 'TTHT và đồng hành',
      forceRender: true,
      children: null
    },
    {
      key: 'hospitals',
      label: 'Cơ sở tế yêu thích',
      forceRender: true,
      children: null
    },
    {
      key: 'service_in_month',
      label: 'CS sức khoẻ toàn diện',
      forceRender: true,
      children: null
    },
    {
      key: 'telemed_doctor_in_month',
      label: 'BS tư vấn khám bệnh qua video',
      forceRender: true,
      children: null
    }
  ]

  const renderTabPane = () => {
    switch (tabActiveKey) {
      case 'partner':
        return (
          <Companionship
            onDragSortTable={onDragSortTable}
            headerRef={headerRef}
            heightContent={heightContent}
            hospitalList={hospitalListTab}
            packageList={packageList}
            loading={loading}
            dataPartner={dataPartner}
            onPressViewDetail={onPressViewDetail}
            actionCompanionship={actionCompanionship}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
            onChangeTabEvent={onChangeTabEvent}
          />
        )
      case 'hospitals':
        return (
          <CSYTFavourite
            onDragSortTable={onDragSortTable}
            headerRef={headerRef}
            heightContent={heightContent}
            hospitalList={hospitalListTab}
            packageList={packageList}
            loading={loading}
            dataCSYTFavourite={dataCSYTFavourite}
            actionCSYTFavourite={actionCSYTFavourite}
            onPressViewDetail={onPressViewDetail}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
            onChangeTabEvent={onChangeTabEvent}
          />
        )
      case 'service_in_month':
        return (
          <HealthCare
            onDragSortTable={onDragSortTable}
            headerRef={headerRef}
            heightContent={heightContent}
            hospitalList={hospitalListTab}
            packageList={packageList}
            loading={loading}
            onPressViewDetail={onPressViewDetail}
            baseValueServiceInMonth={baseValueServiceInMonth}
            loadingServiceInMonth={loadingServiceInMonth}
            methodsServiceInMonth={methodsServiceInMonth}
            category={category}
            setCategory={setCategory}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      case 'telemed_doctor_in_month':
        return (
          <DoctorConsultationVideo
            onDragSortTable={onDragSortTable}
            headerRef={headerRef}
            heightContent={heightContent}
            loading={loadingDoctorTelemed}
            onPressViewDetail={onPressViewDetail}
            baseValueDoctorTelemed={baseValueDoctorTelemed}
            methodsDoctorTelemed={methodsDoctorTelemed}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )

      default:
        return null
    }
  }

  return (
    <div className={styles['brandPage']}>
      <div ref={headerRef} className={styles['brandHeader']}>
        <div className={styles['brandTitle']}>
          <h2>Quảng bá thương hiệu</h2>
        </div>
        <div className={styles['brandActions']}>
          <MPButton typeCustom={'cancel'}>
            <span>Reset cache</span>
          </MPButton>
          <Select
            defaultValue={'MEDPRO'}
            showSearch
            filterOption={(input, option: any) =>
              getReplaceUTF8(
                (option?.children as unknown as string).toLowerCase()
              ).includes(getReplaceUTF8(input.toLowerCase()))
            }
          >
            <Option value={'MEDPRO'}>MEDPRO</Option>
          </Select>
        </div>
      </div>
      <div className={styles['brandBody']}>
        <div className={styles['tabsWrapper']}>
          <Tabs
            defaultActiveKey={'partner'}
            onChange={(key) => {
              setTabActiveKey(key)
              onChangeTabEvent(key, 'generalTab')
            }}
            tabPosition={'top'}
            items={items}
            type={'card'}
          />
        </div>
        {renderTabPane()}
      </div>
    </div>
  )
}

export default MPBrandPromotionCard
