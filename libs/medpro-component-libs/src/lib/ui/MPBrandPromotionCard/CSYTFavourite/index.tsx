import {
  MPButton,
  normalizeTable,
  useDynamicContentHeight
} from '@medpro-libs/medpro-component-libs'
import {
  Image as ImageAtd,
  Modal,
  Switch,
  type TableColumnsType,
  Tabs
} from 'antd'
import { useMemo, useRef, useState } from 'react'
import { FiPlus } from 'react-icons/fi'
import MPTable, { DragHandle } from '../../../atoms/MPTable'
import styles from '../styles.module.less'
import CSYTFavouriteDetail from './CSYTFavouriteDetail'
import CSYTFavouriteNew from './CSYTFavouriteNew'
import dayjs from 'dayjs'

export interface Props {
  onDragSortTable?: any
  headerRef: any
  heightContent: number
  hospitalList: any
  packageList: any
  loading?: boolean
  dataCSYTFavourite: any
  actionCSYTFavourite: any
  onPressViewDetail: (id: string) => Promise<any>
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  onChangeTabEvent: (key: any, level: any) => void
}

const CSYTFavourite = ({
  onDragSortTable,
  headerRef,
  heightContent,
  hospitalList,
  packageList,
  loading,
  dataCSYTFavourite,
  actionCSYTFavourite,
  onPressViewDetail,
  onChangePageEvent,
  onChangeSizeEvent,
  onChangeTabEvent
}: Props) => {
  const filterRef = useRef<HTMLDivElement>(null)
  const { heightContent: dynHeightContent, heightTbBody } =
    useDynamicContentHeight([headerRef.current, filterRef.current], {
      key: 'table',
      heightContent: heightContent - 70
    })
  const [isOpenNew, setIsOpenNew] = useState(false)
  const [isOpenEdit, setIsOpenEdit] = useState(false)
  const [tabActiveKey, setTabActiveKey] = useState('ALL')
  const [detailData, setDetailData] = useState<any>(null)

  const contentModal = {
    create: {
      title: 'Thêm cơ sở y tế yêu thích',
      centered: true,
      footer: false,
      className: styles['modalNewWrapper'],
      width: 800
    },
    edit: {
      title: 'Chỉnh sửa cơ sở y tế yêu thích',
      centered: true,
      footer: false,
      className: styles['modalEditWrapper'],
      width: 800
    }
  }

  const onPressDelete = (row: any) => {
    Modal.confirm({
      title: 'Xác nhận xóa!',
      content: (
        <div className={styles['deleteConfirm']}>
          <p className={styles['deleteText']}>
            Bạn có chắc chắn muốn xóa{' '}
            <span className={styles['spanName']}>{row.hospital?.name}</span>?
          </p>
        </div>
      ),
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      centered: true,
      onOk() {
        return actionCSYTFavourite.delete(row._id)
      }
    })
  }

  const onPressEdit = (row: any) => {
    try {
      setDetailData(row)
      setIsOpenEdit(true)
    } catch (error) {
      console.error('Error setting detail data:', error)
    }
  }

  const onPressNew = () => {
    setIsOpenNew(!isOpenNew)
  }

  const dataSource = useMemo(() => {
    if (!dataCSYTFavourite?.length) return []
    return dataCSYTFavourite.map((item: any, index: number) => {
      return {
        ...item,
        key: item._id || index,
        index,
        displayPeriod: {
          startDate: dayjs(item.fromDate).format('DD/MM/YYYY HH:mm'),
          endDate: dayjs(item.toDate).format('DD/MM/YYYY HH:mm')
        },
        status: item.status
      }
    })
  }, [dataCSYTFavourite])

  const columns: TableColumnsType<any> = normalizeTable(
    [
      {
        title: 'STT',
        width: 70,
        key: 'position',
        align: 'center',
        fixed: 'left',
        onCell: () => ({ style: { padding: '12px 10px' } }),
        render: (text: any, record: any, index: number) => {
          return <div className={styles['groupPosition']}>{index + 1}</div>
        }
      },
      {
        title: 'Thông tin cơ sở y tế',
        width: 350,
        key: 'name',
        fixed: 'left',
        render: (row: any) => {
          return (
            <div className={styles['itemInfo']}>
              {tabActiveKey === 'ALL' && <DragHandle />}
              <div className={styles['itemContent']}>
                <div className={styles['itemImage']}>
                  <ImageAtd
                    src={row.hospital?.circleLogo || row.hospital?.image}
                    alt={row.hospital?.name}
                    width={50}
                    height={50}
                  />
                </div>
                <div className={styles['itemText']}>{row.hospital?.name}</div>
              </div>
            </div>
          )
        }
      },
      {
        title: 'Thời gian hiển thị',
        key: 'displayPeriod',
        width: 200,
        render: (row: any) => {
          return (
            <>
              <div className={styles['periodInfo']}>
                <div className={styles['periodInfoDate']}>
                  <div className={styles['startDate']}>
                    {row?.displayPeriod?.startDate}
                  </div>
                  <div className={styles['dateLine']} />
                  <div className={styles['endDate']}>
                    {row?.displayPeriod?.endDate}
                  </div>
                </div>
                {row?.tabStatus?.text && (
                  <div
                    className={styles['statusBadge']}
                    style={{
                      color: row?.tabStatus?.style?.color,
                      backgroundColor: row?.tabStatus?.style?.backgroundColor,
                      border: `1px solid ${row?.tabStatus?.style?.color}`
                    }}
                  >
                    {row?.tabStatus?.text}
                  </div>
                )}
              </div>
            </>
          )
        }
      },
      {
        title: 'Bật / Tắt Ghim',
        key: 'status',
        align: 'center',
        width: 120,
        render: (row) => <Switch checked={row.status} />
      },
      {
        title: 'Thao tác',
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: 120,
        actionType: 'actionButton',
        actionDisplay: ['edit', 'delete']
      }
    ],
    onPressEdit,
    onPressDelete
  )

  const items = [
    {
      key: 'ALL',
      label: 'Tất cả',
      forceRender: true,
      children: null
    },
    {
      key: 'INPROGRESS',
      label: 'Đang hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: 'PENDING',
      label: 'Chờ hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: 'EXPIRED',
      label: 'Hết hạn',
      forceRender: true,
      children: null
    }
  ]

  const renderTabPane = () => {
    switch (tabActiveKey) {
      case 'ALL':
        return (
          <MPTable
            type={'dragTable'}
            columns={columns}
            dataSource={dataSource}
            dynHeightContent={dynHeightContent}
            heightTbBody={heightTbBody}
            loading={loading}
            pagination={false}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
            onDragSortTable={onDragSortTable}
          />
        )
      case 'INPROGRESS':
      case 'PENDING':
      case 'EXPIRED':
        return (
          <MPTable
            columns={columns}
            dataSource={dataSource}
            dynHeightContent={dynHeightContent}
            heightTbBody={heightTbBody}
            loading={loading}
            pagination={false}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      default:
        return null
    }
  }

  return (
    <>
      <div className={styles['compTabWrapper']}>
        <Tabs
          activeKey={tabActiveKey}
          onChange={(key) => {
            setTabActiveKey(key)
            onChangeTabEvent(key, 'filterTab')
          }}
          tabPosition={'top'}
          tabBarGutter={12}
          items={items}
        />
      </div>
      <div ref={filterRef} className={styles['filterWrapper']}>
        <div className={styles['left']}>
          <div className={styles['recordCount']}>
            {dataSource.length} Cơ sở y tế
          </div>
        </div>
        {tabActiveKey === 'ALL' && (
          <div className={styles['right']}>
            <div className={styles['actionGroup']}>
              <MPButton onClick={onPressNew} typeCustom={'primary'}>
                <FiPlus />
                <span>Thêm cơ sở y tế</span>
              </MPButton>
            </div>
          </div>
        )}
      </div>
      {renderTabPane()}
      {isOpenNew && (
        <CSYTFavouriteNew
          loading={loading}
          contentModal={contentModal.create}
          isOpenNew={isOpenNew}
          setIsOpenNew={setIsOpenNew}
          actionCSYTFavourite={actionCSYTFavourite}
          hospitalList={hospitalList}
        />
      )}
      {isOpenEdit && (
        <CSYTFavouriteDetail
          loading={loading}
          contentModal={contentModal.edit}
          isOpenEdit={isOpenEdit}
          setIsOpenEdit={setIsOpenEdit}
          actionCSYTFavourite={actionCSYTFavourite}
          hospitalList={hospitalList}
          detailData={detailData}
        />
      )}
    </>
  )
}

export default CSYTFavourite
